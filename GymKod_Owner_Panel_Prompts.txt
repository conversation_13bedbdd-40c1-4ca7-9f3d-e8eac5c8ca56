=====================================================
GYMKOD PRO - OWNER PANELİ GELİŞTİRME PROMPTLARI
=====================================================

Bu dosya, GymKod Pro sisteminin Owner paneli için geliştirilebilecek özelliklerin 
detaylı prompt'larını içermektedir. Her prompt, AI'ya verilerek ilgili özelliğin 
geliştirilmesi sağlanabilir.

=====================================================
PROMPT 1: Salon Performans Dashboard
=====================================================

Her salonumun ayrı ayrı performansını görebileceğim salon kontrol paneli oluştur. Bu panel:

- Seçili salon için detaylı metrikleri göstersin
- Üye sayısı trendlerini göstersin (yeni üye, ayrılan üye, aktif üye)
- Salon gelir-gider analizini yapsın
- Üyelik türlerine göre dağılımı göstersin
- Salon doluluk oranlarını göstersin
- Çalışan sayısı ve performansını göstersin
- Diğer salonlarla karşılaştırma özelliği olsun

Company context sistemini kullanarak salon bazında veri getirsin.

YAPILACAKLAR:
- Angular'da yeni component oluştur (salon-performance-dashboard)
- Backend'de salon performans metrikleri için API endpoint'leri ekle
- Chart.js ile interaktif grafikler oluştur
- Salon seçici dropdown komponenti ekle
- Real-time veri güncellemesi için SignalR entegrasyonu

=====================================================
PROMPT 2: Finansal Analiz Raporu 
=====================================================

Tüm salonlarımın finansal durumunu analiz edebileceğim kapsamlı finansal rapor paneli oluştur:

- Salon bazında karlılık analizi yapsın
- Gelir kaynaklarını kategorize etsin (üyelik, ürün satışı, diğer)
- Gider kategorilerini analiz etsin (personel, kira, elektrik vb.)
- Aylık/yıllık büyüme oranlarını hesaplasın
- ROI (yatırım getirisi) hesaplaması yapsın
- Bütçe vs gerçekleşen karşılaştırması yapsın
- Tahmin ve projeksiyon grafikleri göstersin

YAPILACAKLAR:
- Financial analytics service oluştur
- Expense ve Revenue kategorileri için yeni entity'ler ekle
- ROI hesaplama algoritmaları geliştir
- Excel export özelliği ekle
- Tarih aralığı filtreleme sistemi oluştur

=====================================================
PROMPT 3: Akıllı Bildirim Sistemi
=====================================================

Owner olarak tüm salonlarımdan gelen kritik durumları anlık takip edebileceğim 
akıllı bildirim sistemi oluştur:

- Lisansı yakında dolacak salonlar için uyarı
- Ödeme gecikmeleri için bildirim
- Sistem hataları ve performans sorunları uyarısı
- Yeni üye kayıtları bildirimi
- Anormal aktivite tespiti (çok fazla üye kaybı vb.)
- Özelleştirilebilir bildirim ayarları
- E-posta ve SMS entegrasyonu
- Bildirim geçmişi ve takip sistemi

YAPILACAKLAR:
- Notification service ve entity'leri oluştur
- SignalR hub'ı için notification sistemi geliştir
- E-posta template'leri oluştur
- SMS API entegrasyonu (Netgsm/İletimerkezi)
- Notification preferences sayfası oluştur
- Background job'lar için Hangfire entegrasyonu

=====================================================
PROMPT 4: Admin Yönetim Paneli
=====================================================

Salon sahiplerini (adminleri) member'ları yönetir gibi kolay yönetebileceğim 
kapsamlı admin yönetim paneli oluştur:

- Admin listesi (filtreleme, arama, sıralama)
- Admin detay sayfası (salon bilgileri, performans, üyeler)
- Admin ekleme/düzenleme/silme işlemleri
- Toplu işlemler (lisans uzatma, bildirim gönderme)
- Admin aktivite logları
- Salon değiştirme/transfer işlemleri
- Admin performans skorlaması
- Hızlı eylem butonları (bloke et, aktifleştir, mesaj gönder)

YAPILACAKLAR:
- Admin management component'leri oluştur
- Bulk operations için backend API'ları geliştir
- Admin activity logging sistemi ekle
- Performance scoring algoritması oluştur
- Modal dialog'lar için reusable component'ler
- Data table component'ini geliştir

=====================================================
PROMPT 5: Gerçek Zamanlı İzleme Dashboard'u
=====================================================

Tüm salonlarımın anlık durumunu görebileceğim gerçek zamanlı izleme paneli oluştur:

- Anlık aktif üye sayıları
- Günlük giriş-çıkış istatistikleri
- Canlı gelir takibi
- Sistem sağlık durumu (server, database, cache)
- Aktif cihaz sayıları
- Hata logları ve uyarılar
- Network trafiği ve performans metrikleri
- Coğrafi dağılım haritası

YAPILACAKLAR:
- Real-time dashboard component'i oluştur
- SignalR ile canlı veri akışı sağla
- System health monitoring servisleri ekle
- Google Maps API entegrasyonu
- Performance counter'lar için Windows Performance Toolkit
- Redis cache monitoring ekle

=====================================================
PROMPT 6: Mobil Owner Uygulaması
=====================================================

Flutter ile owner için mobil uygulama geliştir. Bu uygulama:

- Temel dashboard metrikleri göstersin
- Push notification'lar alsın
- Hızlı onay işlemleri yapabilsin (lisans uzatma, admin onaylama)
- Acil durum müdahaleleri yapabilsin
- Salon listesi ve hızlı erişim
- Offline mod desteği
- Biometric authentication
- Dark/Light tema desteği

YAPILACAKLAR:
- Flutter owner app projesi oluştur
- Firebase push notification entegrasyonu
- Biometric authentication (local_auth package)
- Offline storage için Hive/SQLite
- State management için Riverpod
- API client'ı oluştur
- App store deployment hazırlıkları

=====================================================
PROMPT 7: Gelişmiş Raporlama Sistemi
=====================================================

Detaylı analiz ve raporlama yapabileceğim kapsamlı raporlama sistemi oluştur:

- Özelleştirilebilir rapor şablonları
- Drag & drop rapor builder
- Scheduled raporlar (otomatik e-posta gönderimi)
- Excel, PDF, CSV export seçenekleri
- Grafik ve tablo kombinasyonları
- Karşılaştırmalı analizler
- Trend analizi ve tahminleme
- Rapor paylaşım sistemi

YAPILACAKLAR:
- Report builder component'i oluştur
- PDF generation için iTextSharp
- Excel export için EPPlus
- Scheduled jobs için Hangfire
- Chart.js ile dinamik grafik sistemi
- Report template engine oluştur

=====================================================
PROMPT 8: Güvenlik ve Audit Sistemi
=====================================================

Sistem güvenliğini ve kullanıcı aktivitelerini takip edebileceğim 
kapsamlı güvenlik paneli oluştur:

- Kullanıcı giriş logları ve analizi
- Şüpheli aktivite tespiti
- IP bazlı erişim kontrolü
- Cihaz yönetimi ve onaylama sistemi
- Oturum yönetimi (force logout)
- Güvenlik raporu ve uyarıları
- Two-factor authentication yönetimi
- API kullanım istatistikleri

YAPILACAKLAR:
- Security audit logging sistemi oluştur
- Suspicious activity detection algoritmaları
- IP whitelist/blacklist yönetimi
- Device management API'ları
- 2FA implementation (Google Authenticator)
- Security dashboard component'i

=====================================================
PROMPT 9: Toplu İşlem Yönetimi
=====================================================

Birden fazla salon/admin üzerinde aynı anda işlem yapabileceğim 
toplu işlem yönetim sistemi oluştur:

- Çoklu seçim ve filtreleme
- Toplu lisans uzatma/iptal etme
- Grup halinde bildirim gönderme
- Toplu ayar değişiklikleri
- Batch backup işlemleri
- Progress tracking ve cancel özelliği
- İşlem geçmişi ve geri alma
- Scheduled bulk operations

YAPILACAKLAR:
- Bulk operations service oluştur
- Progress tracking için SignalR
- Background job processing
- Rollback mechanism oluştur
- Bulk operation history logging
- UI için multi-select component'ler

=====================================================
PROMPT 10: AI Destekli Analitik Sistemi
=====================================================

Yapay zeka destekli analiz ve öngörü sistemi oluştur:

- Üye kaybı risk analizi
- Gelir tahminleme modelleri
- Optimal fiyatlandırma önerileri
- Sezonsal trend analizi
- Anomali tespiti
- Otomatik insight'lar ve öneriler
- Predictive maintenance uyarıları
- Market analizi ve rekabet takibi

YAPILACAKLAR:
- Machine learning model'leri oluştur (Python/ML.NET)
- Prediction API'ları geliştir
- Data preprocessing pipeline'ı kur
- AI insights dashboard component'i
- Model training ve validation sistemi
- External data source entegrasyonları
